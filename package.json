{"scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --write .", "format:check": "prettier --check .", "prepare": "husky install"}, "dependencies": {"@ai-sdk/google": "^1.2.17", "@aws-sdk/client-s3": "^3.806.0", "@aws-sdk/s3-request-presigner": "^3.806.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-accordion": "^1.2.10", "@radix-ui/react-alert-dialog": "^1.1.13", "@radix-ui/react-aspect-ratio": "^1.1.6", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-collapsible": "^1.1.10", "@radix-ui/react-context-menu": "^2.2.14", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-hover-card": "^1.1.13", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-menubar": "^1.1.14", "@radix-ui/react-navigation-menu": "^1.2.12", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-progress": "^1.1.6", "@radix-ui/react-radio-group": "^1.3.6", "@radix-ui/react-scroll-area": "^1.2.8", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slider": "^1.3.4", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-toggle": "^1.1.8", "@radix-ui/react-toggle-group": "^1.1.9", "@radix-ui/react-tooltip": "^1.2.6", "@react-email/components": "^0.0.41", "@react-email/render": "^1.1.2", "@tabler/icons-react": "^3.31.0", "@tanstack/react-query": "^5.75.7", "@tanstack/react-query-devtools": "^5.75.7", "@tanstack/react-table": "^8.21.3", "@types/pdf-parse": "^1.1.5", "ai": "^4.3.15", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "framer-motion": "^12.10.5", "input-otp": "^1.4.2", "lucide-react": "^0.509.0", "mongoose": "^8.14.2", "next": "15.3.2", "next-auth": "5.0.0-beta.28", "next-intl": "^4.1.0", "next-share": "^0.27.0", "next-themes": "^0.4.6", "nextjs-toploader": "^3.8.16", "nuqs": "^2.4.3", "pdf-parse": "^1.1.1", "react": "^19.0.0", "react-audio-analyser": "^1.0.0", "react-day-picker": "8.10.1", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.56.3", "react-intersection-observer": "^9.16.0", "react-markdown": "^10.1.0", "react-mic": "^12.4.6", "react-resizable-panels": "^3.0.1", "react-webcam": "^7.2.0", "recharts": "^2.15.3", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.1", "resend": "^4.5.1", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "uuid": "^9.0.1", "vaul": "^1.1.2", "zod": "^3.24.4"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/uuid": "^9.0.8", "eslint": "^9", "eslint-config-next": "15.3.2", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.0", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-unused-imports": "^4.1.4", "husky": "^9.1.7", "lint-staged": "^16.1.0", "prettier": "^3.5.3", "tailwindcss": "^4", "tsx": "^4.19.4", "tw-animate-css": "^1.2.9", "typescript": "^5"}}