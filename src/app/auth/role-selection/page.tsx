'use client';

import { motion } from 'framer-motion';
import { useRouter, useSearchParams } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { useTranslations } from 'next-intl';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

import { AnimatedBackground } from '@/components/ui/animated-background';
import { AnimatedAuthCard } from '@/components/ui/auth-card';
import { Button } from '@/components/ui/button';

export default function RoleSelectionPage() {
  const t = useTranslations('Auth');
  const router = useRouter();
  const searchParams = useSearchParams();
  const { data: session, update } = useSession();
  const [isLoading, setIsLoading] = useState(false);
  const [selectedRole, setSelectedRole] = useState<'recruiter' | 'candidate' | null>(null);

  const callbackUrl = searchParams.get('callbackUrl') || '/dashboard';

  useEffect(() => {
    // If user already has a role, redirect to dashboard
    if (session?.user?.role && session.user.role !== 'candidate') {
      router.push(callbackUrl);
    }
  }, [session, router, callbackUrl]);

  const handleRoleSelection = async (role: 'recruiter' | 'candidate') => {
    if (!session?.user?.email) {
      toast.error('Session not found. Please try logging in again.');
      return;
    }

    setIsLoading(true);
    setSelectedRole(role);

    try {
      const response = await fetch('/api/auth/set-role', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: session.user.email,
          role,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to set role');
      }

      // Update the session with the new role and clear role selection flag
      await update({
        ...session,
        user: {
          ...session.user,
          role,
          needsRoleSelection: false,
        },
      });

      toast.success(`Role set as ${role} successfully!`);
      router.push(callbackUrl);
    } catch (error) {
      console.error('Role selection error:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to set role');
    } finally {
      setIsLoading(false);
      setSelectedRole(null);
    }
  };

  const fadeIn = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 },
  };

  const staggerContainer = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  return (
    <AnimatedBackground patternColor="primary" colorScheme="indigo">
      <div className="w-full max-w-md px-4">
        <AnimatedAuthCard
          title="Select Your Role"
          description="Choose your role to complete your account setup"
          colorScheme="indigo"
          contentClassName="flex flex-col space-y-4"
        >
          <motion.div initial="hidden" animate="visible" variants={staggerContainer}>
            <motion.div variants={fadeIn} className="mb-4">
              <Button
                variant="default"
                className="w-full"
                onClick={() => handleRoleSelection('recruiter')}
                disabled={isLoading}
              >
                {isLoading && selectedRole === 'recruiter' ? (
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                    <span>Setting up...</span>
                  </div>
                ) : (
                  'I am a Recruiter'
                )}
              </Button>
            </motion.div>

            <motion.div variants={fadeIn}>
              <Button
                variant="outline"
                className="w-full"
                onClick={() => handleRoleSelection('candidate')}
                disabled={isLoading}
              >
                {isLoading && selectedRole === 'candidate' ? (
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
                    <span>Setting up...</span>
                  </div>
                ) : (
                  'I am a Candidate'
                )}
              </Button>
            </motion.div>
          </motion.div>
        </AnimatedAuthCard>
      </div>
    </AnimatedBackground>
  );
}
