import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

import { connectToDatabase } from '@/lib/mongodb';
import User from '@/models/user';

// Define role setting schema for validation
const setRoleSchema = z.object({
  email: z.string().email('Invalid email address'),
  role: z.enum(['recruiter', 'candidate']),
});

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();

    // Validate request body
    const { email, role } = setRoleSchema.parse(body);

    // Connect to database
    await connectToDatabase();

    // Find user by email
    const user = await User.findOne({ email });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Check if user is a Google OAuth user
    if (user.authProvider !== 'google') {
      return NextResponse.json(
        { error: 'Role can only be set for Google OAuth users' },
        { status: 400 }
      );
    }

    // Update user role
    user.role = role;
    await user.save();

    return NextResponse.json({
      success: true,
      message: 'Role updated successfully',
      user: {
        id: user._id.toString(),
        name: user.name,
        email: user.email,
        role: user.role,
        isActive: user.isActive,
      },
    });
  } catch (error) {
    console.error('Set role error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: error.errors[0].message }, { status: 400 });
    }

    return NextResponse.json({ error: 'Something went wrong. Please try again.' }, { status: 500 });
  }
}
