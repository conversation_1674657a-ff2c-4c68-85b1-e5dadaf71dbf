import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

import { connectToDatabase } from '@/lib/mongodb';
import User, { IUser } from '@/models/user';

// Define Google sign-in schema for validation
const googleSignInSchema = z.object({
  email: z.string().email('Invalid email address'),
  name: z.string().min(1, 'Name is required'),
  image: z.string().url().optional(),
  googleId: z.string().min(1, 'Google ID is required'),
});

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();

    // Validate request body
    const { email, name, image, googleId } = googleSignInSchema.parse(body);

    // Connect to database
    await connectToDatabase();

    // Check if user already exists
    let user = (await User.findOne({ email })) as IUser | null;

    if (user) {
      // Existing user - update Google information if needed
      if (!user.googleId && user.authProvider === 'credentials') {
        // User exists with credentials, link Google account
        user.googleId = googleId;
        user.authProvider = 'google';
        user.image = image || user.image;
        await user.save();
      } else if (user.googleId !== googleId) {
        // Update Google ID if different
        user.googleId = googleId;
        user.image = image || user.image;
        await user.save();
      }

      // Check if user is active
      if (user.isActive === false) {
        return NextResponse.json(
          {
            error: 'Your account has been disabled. Please contact an administrator.',
          },
          { status: 403 }
        );
      }

      // Return existing user data
      return NextResponse.json({
        user: {
          id: user._id.toString(),
          name: user.name,
          email: user.email,
          role: user.role,
          isActive: user.isActive,
          needsRoleSelection: false,
        },
      });
    } else {
      // New user - create with default candidate role and mark for role selection
      // Note: Admin role is not available for Google OAuth
      const newUser = new User({
        name,
        email,
        authProvider: 'google',
        googleId,
        image,
        role: 'candidate', // Default role
        isActive: true,
      });

      await newUser.save();

      // Return new user data with role selection flag
      return NextResponse.json({
        user: {
          id: newUser._id.toString(),
          name: newUser.name,
          email: newUser.email,
          role: newUser.role,
          isActive: newUser.isActive,
          needsRoleSelection: true, // New Google users need to select role
        },
      });
    }
  } catch (error) {
    console.error('Google sign-in error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: error.errors[0].message }, { status: 400 });
    }

    return NextResponse.json({ error: 'Something went wrong. Please try again.' }, { status: 500 });
  }
}
