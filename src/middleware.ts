import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';

// This middleware is now simplified since the auth.ts file handles most of the authentication logic

export function middleware(_request: NextRequest) {
  // The middleware is now just a pass-through
  // All authentication logic is handled in the auth.ts file
  return NextResponse.next();
}

// Configure which paths the middleware should run on
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!api|_next/static|_next/image|favicon.ico|.*\\.svg).*)',
  ],
};
