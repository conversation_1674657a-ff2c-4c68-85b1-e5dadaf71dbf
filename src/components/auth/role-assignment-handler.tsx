'use client';

import { useRouter, useSearchParams } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

interface RoleAssignmentHandlerProps {
  children: React.ReactNode;
}

export function RoleAssignmentHandler({ children }: RoleAssignmentHandlerProps) {
  const { data: session, update } = useSession();
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isProcessing, setIsProcessing] = useState(false);

  useEffect(() => {
    const handleRoleAssignment = async () => {
      // Only process if user needs role selection and we haven't started processing
      if (!session?.user?.needsRoleSelection || isProcessing) {
        return;
      }

      // Check for intended role from URL parameters or localStorage
      const urlIntendedRole = searchParams.get('intended_role') as 'recruiter' | 'candidate' | null;
      const localStorageIntendedRole = localStorage.getItem('intended_role') as 'recruiter' | 'candidate' | null;
      const intendedRole = urlIntendedRole || localStorageIntendedRole;

      if (intendedRole) {
        setIsProcessing(true);
        console.log('Auto-assigning role:', intendedRole, 'for user:', session.user.email);

        try {
          const response = await fetch('/api/auth/set-role', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              email: session.user.email,
              role: intendedRole,
            }),
          });

          const data = await response.json();

          if (!response.ok) {
            throw new Error(data.error || 'Failed to set role');
          }

          // Update the session with the new role and clear role selection flag
          await update({
            ...session,
            user: {
              ...session.user,
              role: intendedRole,
              needsRoleSelection: false,
            },
          });

          // Clear the intended role from localStorage
          localStorage.removeItem('intended_role');

          // Remove the intended_role parameter from URL
          const newUrl = new URL(window.location.href);
          newUrl.searchParams.delete('intended_role');
          window.history.replaceState({}, '', newUrl.toString());

          toast.success(`Welcome! Your account has been set up as a ${intendedRole}.`);
        } catch (error) {
          console.error('Role assignment error:', error);
          toast.error('Failed to set up your account. Please try again.');
          // Redirect to role selection page as fallback
          router.push('/auth/role-selection');
        } finally {
          setIsProcessing(false);
        }
      } else if (session.user.needsRoleSelection) {
        // No intended role found, redirect to role selection page
        router.push('/auth/role-selection');
      }
    };

    handleRoleAssignment();
  }, [session, searchParams, router, update, isProcessing]);

  // Show loading state while processing role assignment
  if (session?.user?.needsRoleSelection && isProcessing) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin mx-auto mb-4" />
          <p className="text-muted-foreground">Setting up your account...</p>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}
