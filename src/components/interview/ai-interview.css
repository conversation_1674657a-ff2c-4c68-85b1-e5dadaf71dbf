/* Interview components styling with dark/light mode support */

/* AI Interviewer Styles */
.ai-interviewer-placeholder {
  position: relative;
  backdrop-filter: blur(8px);
  transition: all 0.3s ease;
}

/* Light mode specific styles */
:root {
  --ai-gradient-start: rgba(59, 130, 246, 0.05);
  --ai-gradient-end: rgba(139, 92, 246, 0.1);
  --ai-border: rgba(59, 130, 246, 0.2);
  --ai-shadow: rgba(59, 130, 246, 0.15);
  --ai-text: #1e293b;
  --ai-bg-pattern: rgba(59, 130, 246, 0.03);
}

/* Dark mode specific styles */
.dark {
  --ai-gradient-start: rgba(59, 130, 246, 0.15);
  --ai-gradient-end: rgba(139, 92, 246, 0.25);
  --ai-border: rgba(59, 130, 246, 0.3);
  --ai-shadow: rgba(59, 130, 246, 0.25);
  --ai-text: #f8fafc;
  --ai-bg-pattern: rgba(59, 130, 246, 0.07);
}

/* Shared styles */
.ai-interview-container {
  background: linear-gradient(135deg, var(--ai-gradient-start), var(--ai-gradient-end));
  border: 1px solid var(--ai-border);
  box-shadow: 0 8px 32px var(--ai-shadow);
  color: var(--ai-text);
}

.ai-interviewer-icon {
  filter: drop-shadow(0 4px 8px var(--ai-shadow));
  transform-origin: center;
  transition: transform 0.3s ease;
}

.ai-interviewer-icon:hover {
  transform: scale(1.05);
}

/* Animated elements */
@keyframes pulse-light {
  0%,
  100% {
    opacity: 0.4;
  }
  50% {
    opacity: 0.7;
  }
}

.ai-pulse-animation {
  animation: pulse-light 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* AI Agent Thinking State Animations */
@keyframes thinking-gradient {
  0% {
    background-position: 0% 50%;
    opacity: 0.3;
  }
  25% {
    opacity: 0.6;
  }
  50% {
    background-position: 100% 50%;
    opacity: 0.8;
  }
  75% {
    opacity: 0.6;
  }
  100% {
    background-position: 0% 50%;
    opacity: 0.3;
  }
}

@keyframes thinking-breathe {
  0%,
  100% {
    transform: scale(1);
    opacity: 0.4;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
}

@keyframes thinking-ripple {
  0% {
    transform: scale(0.8);
    opacity: 0.8;
  }
  100% {
    transform: scale(2);
    opacity: 0;
  }
}

.ai-thinking-gradient {
  background: linear-gradient(
    45deg,
    var(--ai-gradient-start),
    var(--ai-gradient-end),
    var(--ai-gradient-start)
  );
  background-size: 200% 200%;
  animation: thinking-gradient 3s ease-in-out infinite;
}

.ai-thinking-breathe {
  animation: thinking-breathe 2.5s ease-in-out infinite;
}

.ai-thinking-ripple {
  animation: thinking-ripple 2s ease-out infinite;
}

/* AI Agent Speaking State Animations */
@keyframes speaking-pulse {
  0% {
    transform: scale(1);
    opacity: 0.6;
  }
  10% {
    transform: scale(1.12);
    opacity: 0.95;
  }
  25% {
    transform: scale(0.95);
    opacity: 0.7;
  }
  40% {
    transform: scale(1.08);
    opacity: 0.9;
  }
  55% {
    transform: scale(0.97);
    opacity: 0.75;
  }
  70% {
    transform: scale(1.1);
    opacity: 0.95;
  }
  85% {
    transform: scale(0.96);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 0.6;
  }
}

@keyframes speaking-wave {
  0%,
  100% {
    transform: scaleY(1) scaleX(1);
    opacity: 0.5;
  }
  15% {
    transform: scaleY(1.3) scaleX(1.08);
    opacity: 0.9;
  }
  30% {
    transform: scaleY(0.85) scaleX(0.95);
    opacity: 0.6;
  }
  45% {
    transform: scaleY(1.25) scaleX(1.06);
    opacity: 0.95;
  }
  60% {
    transform: scaleY(0.9) scaleX(0.97);
    opacity: 0.7;
  }
  75% {
    transform: scaleY(1.2) scaleX(1.04);
    opacity: 0.9;
  }
  90% {
    transform: scaleY(0.95) scaleX(0.98);
    opacity: 0.6;
  }
}

@keyframes speaking-glow {
  0% {
    box-shadow: 0 0 20px var(--ai-shadow);
    opacity: 0.4;
  }
  25% {
    box-shadow: 0 0 30px var(--ai-shadow);
    opacity: 0.7;
  }
  50% {
    box-shadow: 0 0 40px var(--ai-shadow);
    opacity: 0.9;
  }
  75% {
    box-shadow: 0 0 25px var(--ai-shadow);
    opacity: 0.6;
  }
  100% {
    box-shadow: 0 0 20px var(--ai-shadow);
    opacity: 0.4;
  }
}

.ai-speaking-pulse {
  animation: speaking-pulse 1.2s ease-in-out infinite;
}

.ai-speaking-wave {
  animation: speaking-wave 0.8s ease-in-out infinite;
}

.ai-speaking-glow {
  animation: speaking-glow 1.5s ease-in-out infinite;
}

/* Accessibility: Respect reduced motion preference */
@media (prefers-reduced-motion: reduce) {
  .ai-thinking-gradient,
  .ai-thinking-breathe,
  .ai-thinking-ripple,
  .ai-speaking-pulse,
  .ai-speaking-wave,
  .ai-speaking-glow {
    animation: none;
  }

  .ai-thinking-gradient {
    opacity: 0.5;
  }

  .ai-speaking-glow {
    box-shadow: 0 0 25px var(--ai-shadow);
    opacity: 0.6;
  }
}

/* AI Interview background circuit patterns */
.ai-circuit-pattern {
  background-image: url('/ai-interview-background.svg');
  background-size: cover;
  background-repeat: no-repeat;
  opacity: var(--ai-bg-pattern);
}

/* Chat container improvements */
.chat-section {
  display: flex;
  flex-direction: column;
  position: relative;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

/* Fixed height message container with proper scrolling */
.chat-section .flex-1 {
  display: flex;
  flex-direction: column;
  height: calc(100% - 110px); /* Adjust based on header and input height */
  min-height: 0; /* Important for Firefox */
}

/* Match video section height on desktop */
@media (min-width: 768px) {
  .interview-layout {
    height: calc(100vh - 20rem);
  }

  .chat-section,
  .video-section {
    height: 100%;
  }
}
